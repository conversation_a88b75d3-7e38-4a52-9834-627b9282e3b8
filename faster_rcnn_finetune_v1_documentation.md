# Faster R-CNN Fine-tuning for Object Detection

## Overview

This document provides comprehensive documentation for the `faster_rcnn_finetune_v1.py` script, which implements a complete pipeline for fine-tuning a Faster R-CNN model on custom object detection datasets using PyTorch and torchvision.

## Table of Contents

1. [Features](#features)
2. [Dependencies](#dependencies)
3. [Architecture Overview](#architecture-overview)
4. [Key Components](#key-components)
5. [Usage Examples](#usage-examples)
6. [Configuration Parameters](#configuration-parameters)
7. [Output Structure](#output-structure)
8. [Best Practices](#best-practices)

## Features

- **Complete Training Pipeline**: End-to-end training from dataset preparation to model evaluation
- **Dataset Management**: Automatic dataset splitting (train/validation/test) with Pascal VOC XML support
- **Model Architecture**: Pre-trained Faster R-CNN with ResNet-50 FPN backbone
- **Training Features**: Early stopping, learning rate scheduling, and loss visualization
- **Inference Pipeline**: Batch inference with NMS and confidence filtering
- **Visualization**: Automatic generation of prediction visualizations with bounding boxes
- **Reproducibility**: Fixed random seeds for consistent results

## Dependencies

```python
import os
import random
import shutil
import xml.etree.ElementTree as ET
from collections import defaultdict
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision
from torchvision import transforms
from torchvision.models.detection import fasterrcnn_resnet50_fpn
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
import torchvision.transforms.functional as F
from sklearn.metrics import average_precision_score
from datetime import datetime
```

## Architecture Overview

The script implements a modular architecture with the following main components:

### 1. Dataset Preparation
- **Pascal VOC XML Parsing**: Extracts bounding box annotations from XML files
- **Class Mapping**: Automatically discovers classes from dataset annotations
- **Dataset Splitting**: Configurable train/validation/test splits with reproducible random seeding

### 2. Custom Dataset Class
- **Dynamic Resizing**: Handles variable input image sizes with target size normalization
- **Bounding Box Scaling**: Automatically scales annotations to match resized images
- **ImageNet Normalization**: Applies standard normalization for pre-trained models

### 3. Model Architecture
- **Base Model**: Faster R-CNN with ResNet-50 FPN backbone
- **Transfer Learning**: Uses pre-trained COCO weights as starting point
- **Custom Head**: Replaces classification head for custom number of classes

### 4. Training Pipeline
- **Optimizer**: SGD with momentum and weight decay
- **Scheduler**: Step learning rate decay
- **Early Stopping**: Prevents overfitting with configurable patience
- **Loss Tracking**: Monitors both training and validation losses

### 5. Inference System
- **Batch Processing**: Processes entire folders of images
- **Post-processing**: Applies confidence filtering and Non-Maximum Suppression (NMS)
- **Visualization**: Generates annotated images with detection results
- **Output Management**: Organized result saving with timestamps

## Key Components

### Dataset Splitting Function

```python
def split_dataset(dataset_folder, output_folder, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15, seed=489):
    """Split dataset into train, validation, and test sets"""
```

**Parameters:**
- `dataset_folder`: Source directory containing images and XML annotations
- `output_folder`: Destination directory for split datasets
- `train_ratio`: Proportion of data for training (default: 0.7)
- `val_ratio`: Proportion of data for validation (default: 0.15)
- `test_ratio`: Proportion of data for testing (default: 0.15)
- `seed`: Random seed for reproducible splits (default: 489)

### Custom Dataset Class

```python
class CustomDataset(Dataset):
    def __init__(self, data_dir, class_to_idx, transforms=None, target_size=(1824, 864)):
```

**Key Features:**
- Handles Pascal VOC format annotations
- Dynamic image resizing with bounding box scaling
- Configurable target image dimensions
- Support for data augmentation transforms

### Model Configuration

```python
def get_model(num_classes):
    """Load pre-trained Faster R-CNN model and modify for custom classes"""
    model = fasterrcnn_resnet50_fpn(pretrained=True)
    in_features = model.roi_heads.box_predictor.cls_score.in_features
    model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
    return model
```

### Training Function

```python
def train_model(model, train_loader, val_loader, device, num_epochs=10, lr=0.005):
    """Complete training pipeline with early stopping"""
```

**Features:**
- SGD optimizer with momentum (0.9) and weight decay (0.0005)
- Step learning rate scheduler (decay every 3 epochs by factor 0.1)
- Early stopping with patience of 5 epochs
- Automatic best model saving
- Training curve visualization

### Inference Pipeline

```python
def predict_images_in_folder(model, folder_path, class_names, device, 
                           conf_threshold=0.5, nms_threshold=0.5, 
                           target_size=(1824, 864), output_folder='inference_results'):
```

**Parameters:**
- `conf_threshold`: Minimum confidence score for detections (default: 0.5)
- `nms_threshold`: IoU threshold for Non-Maximum Suppression (default: 0.5)
- `target_size`: Input image dimensions for model inference
- `output_folder`: Directory for saving visualization results

## Usage Examples

### Complete Training Pipeline

```python
# Set your paths
dataset_folder = "/path/to/your/dataset"  # Contains images and XML files
output_folder = "/path/to/split/dataset"   # Where to save train/val/test splits

# Run complete pipeline
trained_model, class_names, class_to_idx = run_complete_pipeline(
    dataset_folder=dataset_folder,
    output_folder=output_folder,
    num_epochs=10,
    batch_size=2,
    learning_rate=0.005
)

# Save the trained model
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
torch.save(trained_model.state_dict(), f'final_model_{timestamp}.pth')
```

### Inference on Saved Model

```python
# Configuration for inference
model_path = '/path/to/saved/model.pth'
test_folder = '/path/to/test/images'
class_names = ['background', 'your_class_1', 'your_class_2']  # Update with actual classes
output_folder = '/path/to/inference/results'

# Run inference
load_model_and_run_inference(model_path, test_folder, class_names, output_folder)
```

## Configuration Parameters

### Training Configuration

| Parameter | Default Value | Description |
|-----------|---------------|-------------|
| `num_epochs` | 10 | Maximum number of training epochs |
| `batch_size` | 2 | Training batch size |
| `learning_rate` | 0.005 | Initial learning rate |
| `patience` | 5 | Early stopping patience |
| `target_size` | (1824, 864) | Input image dimensions |

### Inference Configuration

| Parameter | Default Value | Description |
|-----------|---------------|-------------|
| `conf_threshold` | 0.5 | Minimum confidence for detections |
| `nms_threshold` | 0.5 | IoU threshold for NMS |
| `target_size` | (1824, 864) | Input image dimensions |

### Data Augmentation

The script uses ImageNet normalization by default:
- Mean: [0.485, 0.456, 0.406]
- Std: [0.229, 0.224, 0.225]

## Output Structure

### Training Outputs
- `best_model.pth`: Best model weights based on validation loss
- `final_model_YYYYMMDD_HHMMSS.pth`: Final model with timestamp
- Training loss curves visualization
- Validation performance metrics

### Inference Outputs
- Annotated images with bounding boxes and confidence scores
- Console output with detection coordinates and scores
- Organized folder structure with timestamped results

### Visualization Features
- Ground truth vs. predictions comparison
- Color-coded bounding boxes (green for ground truth, red for predictions)
- Confidence scores displayed on detections
- High-resolution output images (150 DPI)

## Best Practices

### Dataset Preparation
1. **Consistent Annotation Format**: Ensure all XML files follow Pascal VOC format
2. **Balanced Classes**: Aim for balanced representation across object classes
3. **Quality Control**: Verify annotation accuracy before training
4. **Image Quality**: Use consistent image resolution and quality

### Training Optimization
1. **Batch Size**: Adjust based on GPU memory (start with 2-4)
2. **Learning Rate**: Use learning rate scheduling for better convergence
3. **Early Stopping**: Monitor validation loss to prevent overfitting
4. **Data Augmentation**: Consider adding augmentations for better generalization

### Inference Tuning
1. **Confidence Threshold**: Adjust based on precision/recall requirements
2. **NMS Threshold**: Fine-tune to balance detection accuracy and duplicate removal
3. **Target Size**: Match training dimensions for optimal performance
4. **Batch Processing**: Process images in batches for efficiency

### Performance Monitoring
1. **Loss Curves**: Monitor training and validation loss trends
2. **Visual Inspection**: Regularly check prediction quality on validation set
3. **Metric Tracking**: Implement additional metrics like mAP for comprehensive evaluation
4. **Resource Usage**: Monitor GPU memory and training time

---

*This documentation covers the complete functionality of the faster_rcnn_finetune_v1.py script. For additional support or customization requirements, refer to the inline code comments and PyTorch documentation.*
