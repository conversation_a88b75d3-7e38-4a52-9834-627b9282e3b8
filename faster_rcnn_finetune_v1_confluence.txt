h1. Faster R-CNN Fine-tuning for Object Detection

h2. Overview

This document provides comprehensive documentation for the faster_rcnn_finetune_v1.py script, which implements a complete pipeline for fine-tuning a Faster R-CNN model on custom object detection datasets using PyTorch and torchvision.

h2. Table of Contents

* [Features|#features]
* [Dependencies|#dependencies]
* [Training Set|#training-set]
* [Methodology|#methodology]
* [KPIs (Key Performance Indicators)|#kpis]
* [Architecture Overview|#architecture-overview]
* [Key Components|#key-components]
* [Usage Examples|#usage-examples]
* [Configuration Parameters|#configuration-parameters]
* [Output Structure|#output-structure]
* [Best Practices|#best-practices]

h2. Features

* *Complete Training Pipeline*: End-to-end training from dataset preparation to model evaluation
* *Dataset Management*: Automatic dataset splitting (train/validation/test) with Pascal VOC XML support
* *Model Architecture*: Pre-trained Faster R-CNN with ResNet-50 FPN backbone
* *Training Features*: Early stopping, learning rate scheduling, and loss visualization
* *Inference Pipeline*: Batch inference with NMS and confidence filtering
* *Visualization*: Automatic generation of prediction visualizations with bounding boxes
* *Reproducibility*: Fixed random seeds for consistent results

h2. Dependencies

{code:language=python}
import os
import random
import shutil
import xml.etree.ElementTree as ET
from collections import defaultdict
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchvision
from torchvision import transforms
from torchvision.models.detection import fasterrcnn_resnet50_fpn
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor
import torchvision.transforms.functional as F
from sklearn.metrics import average_precision_score
from datetime import datetime
{code}

h2. Training Set

h3. Dataset Structure and Requirements

The script is designed to work with *Pascal VOC format* datasets, which consist of:

h4. Input Data Format
* *Images*: JPEG, JPG, or PNG format
* *Annotations*: XML files in Pascal VOC format
* *File Naming*: Each image must have a corresponding XML file with the same base name
** Example: image001.jpg → image001.xml

h4. Dataset Organization
{noformat}
dataset_folder/
├── image001.jpg
├── image001.xml
├── image002.jpg
├── image002.xml
├── ...
└── imageN.xml
{noformat}

h4. XML Annotation Structure
{code:language=xml}
<annotation>
    <object>
        <name>class_name</name>
        <bndbox>
            <xmin>100</xmin>
            <ymin>150</ymin>
            <xmax>300</xmax>
            <ymax>400</ymax>
        </bndbox>
    </object>
</annotation>
{code}

h3. Automatic Dataset Splitting

The script automatically splits your dataset into three subsets:

||Split||Default Ratio||Purpose||
|Training|70%|Model training and weight optimization|
|Validation|15%|Hyperparameter tuning and early stopping|
|Test|15%|Final model evaluation and performance assessment|

h4. Split Configuration
* *Reproducible*: Uses fixed random seed (489) for consistent splits
* *Stratified*: Maintains class distribution across splits
* *Configurable*: Ratios can be adjusted via parameters

h3. Data Preprocessing Pipeline

h4. Image Preprocessing
# *Format Conversion*: All images converted to RGB format
# *Resizing*: Images resized to target dimensions (default: 1824×864)
# *Normalization*: ImageNet standard normalization applied
** Mean: [0.485, 0.456, 0.406]
** Std: [0.229, 0.224, 0.225]
# *Tensor Conversion*: PIL Images converted to PyTorch tensors

h4. Annotation Preprocessing
# *Bounding Box Scaling*: Coordinates scaled to match resized images
# *Class Mapping*: String class names mapped to integer indices
# *Format Conversion*: Annotations converted to PyTorch tensors
# *Validation*: Ensures bounding boxes are within image boundaries

h2. Methodology

h3. Transfer Learning Approach

h4. Base Model Selection
* *Architecture*: Faster R-CNN with ResNet-50 FPN (Feature Pyramid Network)
* *Pre-training*: COCO dataset (80 object classes, 330K images)
* *Backbone*: ResNet-50 with Feature Pyramid Network for multi-scale detection
* *Head Replacement*: Custom classification head for target classes

h4. Fine-tuning Strategy
# *Frozen Backbone*: Initial layers retain COCO-learned features
# *Trainable Head*: Only classification layers updated initially
# *Gradual Unfreezing*: Can be extended to fine-tune backbone layers
# *Learning Rate*: Conservative approach (0.005) to preserve pre-trained features

h3. Training Methodology

h4. Optimization Strategy
{code:language=python}
# Optimizer Configuration
optimizer = SGD(
    model.parameters(),
    lr=0.005,           # Conservative learning rate
    momentum=0.9,       # Momentum for stable convergence
    weight_decay=0.0005 # L2 regularization
)

# Learning Rate Scheduling
scheduler = StepLR(
    optimizer,
    step_size=3,        # Decay every 3 epochs
    gamma=0.1           # Reduce by factor of 10
)
{code}

h4. Loss Function
* *Multi-task Loss*: Combines classification and regression losses
* *RPN Loss*: Region Proposal Network loss for object/background classification
* *ROI Loss*: Region of Interest loss for final classification and bounding box regression
* *Automatic Weighting*: PyTorch handles loss component balancing

h4. Training Process
# *Forward Pass*: Images processed through backbone and FPN
# *Region Proposals*: RPN generates potential object locations
# *ROI Pooling*: Features extracted for each proposal
# *Classification*: Final object class prediction
# *Regression*: Bounding box coordinate refinement
# *Loss Computation*: Multi-component loss calculation
# *Backpropagation*: Gradient computation and weight updates

h2. KPIs (Key Performance Indicators)

h3. Training Performance Metrics

h4. Loss Metrics
||Metric||Actual Value||Target Range||Status||
|Training Loss|0.1108|Decreasing trend|{color:green}✅ Good{color}|
|Validation Loss|0.1850|Decreasing trend|{color:green}✅ Good{color}|
|Overfitting Gap|0.0742|< 0.1 units|{color:green}✅ Acceptable{color}|
|Loss Convergence|Stable|< 5% variation|{color:green}✅ Achieved{color}|

h4. Training Efficiency Metrics
||Metric||Actual Value||Target Value||Status||
|Final Training Loss|0.1108|< 0.2|{color:green}✅ Achieved{color}|
|Final Validation Loss|0.1850|< 0.3|{color:green}✅ Achieved{color}|
|Loss Stability|Converged|Stable trend|{color:green}✅ Good{color}|
|Model Convergence|Successful|Early stopping triggered|{color:green}✅ Optimal{color}|

h3. Model Performance Metrics

h4. Detection Accuracy (Test Set Results)
||Metric||Formula||Actual Value||Target Value||Status||
|Precision|TP / (TP + FP)|0.650|> 0.8|{color:red}❌ Below Target{color}|
|Recall|TP / (TP + FN)|0.903|> 0.7|{color:green}✅ Achieved{color}|
|F1-Score|2 × (Precision × Recall) / (Precision + Recall)|0.756|> 0.75|{color:green}✅ Achieved{color}|

h4. Test Set Statistics
||Metric||Value||Description||
|Test Images Processed|40|Total images in test set|
|Total Predictions|100|All detections above confidence threshold|
|Total Ground Truths|72|Actual objects in test set|
|True Positives (TP)|65|Correctly detected objects|
|False Positives (FP)|35|Incorrect detections|
|False Negatives (FN)|7|Missed objects|

h4. Performance Analysis
* *Strength*: High Recall (0.903) - Model successfully detects most traffic signs
* *Weakness*: Lower Precision (0.650) - Model generates some false positive detections
* *Overall*: F1-Score (0.756) meets target, indicating balanced performance
* *Recommendation*: Consider increasing confidence threshold to reduce false positives

h2. Faster R-CNN Architecture Overview

h3. Visual Architecture Diagrams

h4. Overall Faster R-CNN Pipeline

!faster_rcnn_overview.png|thumbnail!

_Figure 1: Complete Faster R-CNN architecture showing the two-stage detection process_

{noformat}
Input Image → Backbone CNN → Feature Maps → RPN → ROI Pooling → Classification & Regression → Output
     ↓              ↓            ↓         ↓         ↓              ↓                    ↓
  (1824×864)    ResNet-50     Multi-scale  Region   Fixed-size    Class + BBox      Detections
                   +FPN       Features    Proposals  Features      Predictions      + Scores
{noformat}

h4. Two-Stage Detection Process

*Stage 1: Region Proposal Network (RPN)*
* Generates object proposals (potential object locations)
* Classifies each proposal as object/background
* Refines bounding box coordinates

*Stage 2: ROI Head (Region of Interest)*
* Takes RPN proposals and extracts features
* Final classification into specific object classes
* Final bounding box regression for precise localization

h3. Key Components Explained

h4. Backbone Network (ResNet-50)
* *Purpose*: Extract hierarchical features from input images
* *Architecture*: 50-layer Residual Network with skip connections
* *Output*: Multi-scale feature maps at different resolutions
* *Pre-training*: Trained on ImageNet for general feature extraction

h4. Feature Pyramid Network (FPN)
* *Purpose*: Combine low-resolution, semantically strong features with high-resolution, semantically weak features
* *Benefit*: Enables detection of objects at multiple scales
* *Implementation*: Top-down pathway with lateral connections

h4. Region Proposal Network (RPN)
* *Input*: Feature maps from FPN
* *Output*: Object proposals with objectness scores
* *Anchors*: Pre-defined bounding boxes at multiple scales and aspect ratios
* *Loss*: Binary classification (object/background) + bounding box regression

h4. ROI Pooling & Head
* *Purpose*: Extract fixed-size features from variable-size proposals
* *Process*: 
## ROI Align: Precise feature extraction from proposals
## Fully connected layers for final classification
## Bounding box regression for coordinate refinement

h3. Training Process Flow

h4. Forward Pass
# *Image Input*: Resize to target dimensions (1824×864)
# *Feature Extraction*: ResNet-50 + FPN generates multi-scale features
# *RPN Processing*: Generates ~2000 object proposals
# *ROI Processing*: Extracts features for top proposals
# *Final Predictions*: Class probabilities + refined bounding boxes

h4. Loss Computation
{noformat}
Total Loss = RPN_cls_loss + RPN_reg_loss + ROI_cls_loss + ROI_reg_loss

Where:
- RPN_cls_loss: Object/background classification in RPN
- RPN_reg_loss: Bounding box regression in RPN  
- ROI_cls_loss: Final object classification
- ROI_reg_loss: Final bounding box refinement
{noformat}

h3. Training Process Visualization

!training_process.png|thumbnail!

_Figure 3: Complete training process flow with actual loss values and performance metrics_

h2. Key Components

h3. Dataset Splitting Function

{code:language=python}
def split_dataset(dataset_folder, output_folder, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15, seed=489):
    """Split dataset into train, validation, and test sets"""
{code}

*Parameters:*
* dataset_folder: Source directory containing images and XML annotations
* output_folder: Destination directory for split datasets
* train_ratio: Proportion of data for training (default: 0.7)
* val_ratio: Proportion of data for validation (default: 0.15)
* test_ratio: Proportion of data for testing (default: 0.15)
* seed: Random seed for reproducible splits (default: 489)

h3. Custom Dataset Class

{code:language=python}
class CustomDataset(Dataset):
    def __init__(self, data_dir, class_to_idx, transforms=None, target_size=(1824, 864)):
{code}

*Key Features:*
* Handles Pascal VOC format annotations
* Dynamic image resizing with bounding box scaling
* Configurable target image dimensions
* Support for data augmentation transforms

h2. Usage Examples

h3. Complete Training Pipeline

{code:language=python}
# Set your paths
dataset_folder = "/path/to/your/dataset"  # Contains images and XML files
output_folder = "/path/to/split/dataset"   # Where to save train/val/test splits

# Run complete pipeline
trained_model, class_names, class_to_idx = run_complete_pipeline(
    dataset_folder=dataset_folder,
    output_folder=output_folder,
    num_epochs=10,
    batch_size=2,
    learning_rate=0.005
)

# Save the trained model
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
torch.save(trained_model.state_dict(), f'final_model_{timestamp}.pth')
{code}

h3. Inference on Saved Model

{code:language=python}
# Configuration for inference
model_path = '/path/to/saved/model.pth'
test_folder = '/path/to/test/images'
class_names = ['background', 'your_class_1', 'your_class_2']  # Update with actual classes
output_folder = '/path/to/inference/results'

# Run inference
load_model_and_run_inference(model_path, test_folder, class_names, output_folder)
{code}

h2. Configuration Parameters

h3. Training Configuration

||Parameter||Default Value||Description||
|num_epochs|10|Maximum number of training epochs|
|batch_size|2|Training batch size|
|learning_rate|0.005|Initial learning rate|
|patience|5|Early stopping patience|
|target_size|(1824, 864)|Input image dimensions|

h3. Inference Configuration

||Parameter||Default Value||Description||
|conf_threshold|0.5|Minimum confidence for detections|
|nms_threshold|0.5|IoU threshold for NMS|
|target_size|(1824, 864)|Input image dimensions|

h2. Best Practices

h3. Dataset Preparation
# *Consistent Annotation Format*: Ensure all XML files follow Pascal VOC format
# *Balanced Classes*: Aim for balanced representation across object classes
# *Quality Control*: Verify annotation accuracy before training
# *Image Quality*: Use consistent image resolution and quality

h3. Training Optimization
# *Batch Size*: Adjust based on GPU memory (start with 2-4)
# *Learning Rate*: Use learning rate scheduling for better convergence
# *Early Stopping*: Monitor validation loss to prevent overfitting
# *Data Augmentation*: Consider adding augmentations for better generalization

h3. Performance Monitoring
# *Loss Curves*: Monitor training and validation loss trends
# *Visual Inspection*: Regularly check prediction quality on validation set
# *Metric Tracking*: Implement additional metrics like mAP for comprehensive evaluation
# *Resource Usage*: Monitor GPU memory and training time

----

_This documentation covers the complete functionality of the faster_rcnn_finetune_v1.py script. For additional support or customization requirements, refer to the inline code comments and PyTorch documentation._
